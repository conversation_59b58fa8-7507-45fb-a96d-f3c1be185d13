# 更新日志

所有重要的项目更改都将记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划功能
- 支持配置文件导入/导出
- 支持代理商配置验证
- 添加配置加密功能
- 支持批量操作

## [1.0.0] - 2025-07-17

### 新增
- 初始版本发布
- 代理商配置的增删改查功能
- 快速切换代理商功能
- 自动管理 shell 配置文件
- 彩色命令行界面
- 一键安装脚本
- 完整的帮助文档
- 错误处理和验证
- 配置持久化存储

### 功能
- `add` - 添加新的代理商配置
- `remove` - 删除代理商配置
- `list` - 列出所有配置的代理商
- `switch` - 切换到指定代理商
- `current` - 显示当前使用的代理商
- `update` - 更新代理商信息
- `help` - 显示帮助信息

### 技术特性
- 支持 macOS 系统
- 自动检测和安装依赖 (jq, Homebrew)
- 支持多种 shell 配置文件
- JSON 格式配置存储
- 安全的本地存储
- 完整的错误处理

### 文档
- 详细的 README.md
- 使用示例文档
- 安装说明
- 故障排除指南
- MIT 许可证